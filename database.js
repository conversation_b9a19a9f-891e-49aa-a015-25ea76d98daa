const sqlite3 = require('sqlite3').verbose();
const path = require('path');
const fs = require('fs');
const config = require('./config');

class Database {
  constructor() {
    this.db = null;
    this.init();
  }

  init() {
    // Create database directory if it doesn't exist
    const dbDir = path.dirname(config.database.path);
    if (!fs.existsSync(dbDir)) {
      fs.mkdirSync(dbDir, { recursive: true });
    }

    // Create backup directory if it doesn't exist
    if (!fs.existsSync(config.database.backup)) {
      fs.mkdirSync(config.database.backup, { recursive: true });
    }

    this.db = new sqlite3.Database(config.database.path, (err) => {
      if (err) {
        console.error('Error opening database:', err.message);
      } else {
        console.log('Connected to SQLite database');
        this.createTables();
      }
    });
  }

  createTables() {
    // Create tables synchronously to ensure proper order
    this.createTablesSync();
  }

  createTablesSync() {
    const tables = [
      // Users table
      `CREATE TABLE IF NOT EXISTS users (
        id TEXT PRIMARY KEY,
        username TEXT NOT NULL,
        email TEXT UNIQUE,
        password TEXT,
        avatar TEXT DEFAULT 'pic.webp',
        country TEXT DEFAULT '--',
        ip TEXT,
        device TEXT,
        rank INTEGER DEFAULT 0,
        likes INTEGER DEFAULT 0,
        dislikes INTEGER DEFAULT 0,
        banned BOOLEAN DEFAULT 0,
        muted BOOLEAN DEFAULT 0,
        verified BOOLEAN DEFAULT 0,
        guest BOOLEAN DEFAULT 1,
        last_seen DATETIME DEFAULT CURRENT_TIMESTAMP,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`,

      // Rooms table
      `CREATE TABLE IF NOT EXISTS rooms (
        id TEXT PRIMARY KEY,
        name TEXT NOT NULL,
        topic TEXT,
        description TEXT,
        password TEXT,
        owner_id TEXT,
        max_users INTEGER DEFAULT 50,
        background_color TEXT DEFAULT '#000000',
        welcome_message TEXT,
        private BOOLEAN DEFAULT 0,
        voice_enabled BOOLEAN DEFAULT 1,
        likes_required INTEGER DEFAULT 0,
        visitor_likes_required INTEGER DEFAULT 0,
        no_guests BOOLEAN DEFAULT 0,
        banner TEXT,
        avatar TEXT DEFAULT 'room.webp',
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (owner_id) REFERENCES users (id)
      )`,

      // Messages table
      `CREATE TABLE IF NOT EXISTS messages (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        room_id TEXT,
        recipient_id TEXT,
        content TEXT NOT NULL,
        message_type TEXT DEFAULT 'text',
        reply_to INTEGER,
        likes INTEGER DEFAULT 0,
        dislikes INTEGER DEFAULT 0,
        deleted BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (room_id) REFERENCES rooms (id),
        FOREIGN KEY (recipient_id) REFERENCES users (id),
        FOREIGN KEY (reply_to) REFERENCES messages (id)
      )`,

      // Wall posts table
      `CREATE TABLE IF NOT EXISTS wall_posts (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        user_id TEXT NOT NULL,
        content TEXT NOT NULL,
        link TEXT,
        reply_to INTEGER,
        likes INTEGER DEFAULT 0,
        dislikes INTEGER DEFAULT 0,
        deleted BOOLEAN DEFAULT 0,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id),
        FOREIGN KEY (reply_to) REFERENCES wall_posts (id)
      )`,

      // Room members table
      `CREATE TABLE IF NOT EXISTS room_members (
        room_id TEXT,
        user_id TEXT,
        role TEXT DEFAULT 'member',
        joined_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        PRIMARY KEY (room_id, user_id),
        FOREIGN KEY (room_id) REFERENCES rooms (id),
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`,

      // Bans table
      `CREATE TABLE IF NOT EXISTS bans (
        id INTEGER PRIMARY KEY AUTOINCREMENT,
        type TEXT NOT NULL,
        value TEXT NOT NULL,
        reason TEXT,
        banned_by TEXT,
        expires_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (banned_by) REFERENCES users (id)
      )`,

      // Sessions table
      `CREATE TABLE IF NOT EXISTS sessions (
        id TEXT PRIMARY KEY,
        user_id TEXT NOT NULL,
        socket_id TEXT,
        ip TEXT,
        user_agent TEXT,
        expires_at DATETIME,
        created_at DATETIME DEFAULT CURRENT_TIMESTAMP,
        FOREIGN KEY (user_id) REFERENCES users (id)
      )`,

      // Settings table
      `CREATE TABLE IF NOT EXISTS settings (
        key TEXT PRIMARY KEY,
        value TEXT,
        updated_at DATETIME DEFAULT CURRENT_TIMESTAMP
      )`
    ];

    // Create tables synchronously
    let completed = 0;
    const totalTables = tables.length;

    tables.forEach((table, index) => {
      this.db.run(table, (err) => {
        if (err) {
          console.error('Error creating table:', err.message);
        } else {
          console.log(`Table ${index + 1}/${totalTables} created successfully`);
        }

        completed++;
        if (completed === totalTables) {
          // All tables created, now insert default settings
          setTimeout(() => {
            this.insertDefaultSettings();
          }, 100);
        }
      });
    });
  }

  insertDefaultSettings() {
    const defaultSettings = [
      ['site_name', config.site.name],
      ['site_title', config.site.title],
      ['site_description', config.site.description],
      ['site_keywords', config.site.keywords],
      ['max_ip', config.site.maxIP.toString()],
      ['wall_likes', config.site.wallLikes.toString()],
      ['wall_minutes', config.site.wallMinutes.toString()],
      ['pm_likes', config.site.pmLikes.toString()],
      ['not_likes', config.site.notLikes.toString()],
      ['files_likes', config.site.filesLikes.toString()],
      ['prof_likes', config.site.profLikes.toString()],
      ['pic_likes', config.site.picLikes.toString()]
    ];

    const stmt = this.db.prepare(`
      INSERT OR IGNORE INTO settings (key, value) VALUES (?, ?)
    `);

    defaultSettings.forEach(([key, value]) => {
      stmt.run(key, value);
    });

    stmt.finalize();
  }

  // Helper methods for database operations
  run(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.run(sql, params, function(err) {
        if (err) {
          reject(err);
        } else {
          resolve({ id: this.lastID, changes: this.changes });
        }
      });
    });
  }

  get(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.get(sql, params, (err, row) => {
        if (err) {
          reject(err);
        } else {
          resolve(row);
        }
      });
    });
  }

  all(sql, params = []) {
    return new Promise((resolve, reject) => {
      this.db.all(sql, params, (err, rows) => {
        if (err) {
          reject(err);
        } else {
          resolve(rows);
        }
      });
    });
  }

  close() {
    return new Promise((resolve, reject) => {
      this.db.close((err) => {
        if (err) {
          reject(err);
        } else {
          console.log('Database connection closed');
          resolve();
        }
      });
    });
  }
}

module.exports = new Database();
